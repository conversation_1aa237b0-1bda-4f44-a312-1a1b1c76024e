'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { ContractorDetails } from '@/components/ui/contractor-details';
import { IncompleteProfileCard } from '@/components/ui/incomplete-profile-card';
import {
  AdminOnboarding,
  AdminOnboardingFormValues,
  useCompleteAdminOnboarding,
} from '@/features/admin-onboarding';
import {
  ContractorOnboarding,
  FullFormValues,
  useCompleteContractorOnboarding,
} from '@/features/contractor-onboarding';
import { useUserWithProfile } from '@/hooks/use-auth';
import { useContractorProfile } from '@/hooks/use-contractor-profile';
import { useForceProfileRefresh } from '@/hooks/use-force-profile-refresh';
import { usePermissions } from '@/hooks/use-permissions';
import {
  AlertTriangle,
  Building,
  CheckCircle,
  Settings,
  Calendar,
  UserCheck,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import * as React from 'react';

export default function ProfilePage() {
  const {
    data: user,
    isLoading: userLoading,
    refetch: _refetchUser,
  } = useUserWithProfile();
  const {
    userRole,
    isContractor,
    isJKR,
    isClient,
    isLoading: permissionsLoading,
  } = usePermissions();
  const { data: contractorData, isLoading: contractorDataLoading } =
    useContractorProfile(user?.id, isContractor);
  const completeContractorOnboarding = useCompleteContractorOnboarding();
  const completeAdminOnboarding = useCompleteAdminOnboarding();
  const router = useRouter();
  const forceProfileRefresh = useForceProfileRefresh();

  const formatDate = (dateString: string | null) => {
    if (!dateString) return t('companyInformation.notSpecified');
    return new Date(dateString).toLocaleDateString('en-MY', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Translation hooks
  const t = useTranslations('profilePage');
  const _common = useTranslations('common');

  const isLoading = userLoading || permissionsLoading;
  const onboardingCompleted = user?.profile?.onboarding_completed ?? false;

  // Simple check to ensure we have essential data before rendering
  const hasEssentialData =
    !isLoading && user?.profile && userRole !== undefined;

  // Handle invitation flow - force refresh when coming from invitation or when refresh=true
  React.useEffect(() => {
    // Only run on client side
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const shouldRefresh = urlParams.get('refresh') === 'true';
      const fromInvitation = urlParams.get('from') === 'invitation';

      if (shouldRefresh || fromInvitation) {
        // Remove the refresh parameter from URL first
        if (shouldRefresh) {
          const newUrl = window.location.pathname;
          window.history.replaceState({}, '', newUrl);
        }

        // Clear middleware cookies to ensure fresh data
        document.cookie =
          'user_role=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
        document.cookie =
          'onboarding_completed=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';

        // Force refresh all profile data
        forceProfileRefresh();
      }
    }
  }, [forceProfileRefresh]);

  // Fallback: Auto-refresh if we're a contractor but still showing incomplete after some time
  React.useEffect(() => {
    if (isContractor && !onboardingCompleted && !isLoading) {
      // Check if we just completed onboarding by looking at localStorage (only on client)
      if (typeof localStorage !== 'undefined') {
        const recentlyCompleted = localStorage.getItem(
          'onboarding_just_completed',
        );
        if (recentlyCompleted) {
          // Clear the flag and force refresh after a short delay
          localStorage.removeItem('onboarding_just_completed');
          setTimeout(() => {
            forceProfileRefresh();
          }, 2000);
        }
      }
    }
  }, [isContractor, onboardingCompleted, isLoading, forceProfileRefresh]);

  const handleSubmit = async (values: FullFormValues) => {
    try {
      await completeContractorOnboarding.mutateAsync(values);
    } catch (error) {
      console.error('Contractor onboarding submission error:', error);
    }
  };

  const _handleAdminOnboardingSubmit = async (
    values: AdminOnboardingFormValues,
  ) => {
    try {
      await completeAdminOnboarding.mutateAsync(values);
      // Force refresh to update UI
      forceProfileRefresh();
    } catch (error) {
      console.error('Admin onboarding submission error:', error);
    }
  };

  const handleSkipToCompletion = async () => {
    // Clear onboarding and user role cookies to force refresh
    if (typeof document !== 'undefined') {
      document.cookie =
        'onboarding_completed=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
      document.cookie =
        'user_role=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    }

    // Force refresh profile data
    forceProfileRefresh();

    // User already has contractor_id, redirect to dashboard
    router.push('/dashboard');
  };
  // Loading state with better design
  if (isLoading || !hasEssentialData) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8 lg:py-12">
          <div className="w-full max-w-4xl mx-auto">
            <Card>
              <CardContent className="flex items-center justify-center py-16">
                <div className="flex flex-col items-center space-y-4">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
                  <div className="text-center space-y-2">
                    <p className="text-lg font-medium text-foreground">
                      {t('loading.title')}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {t('loading.description')}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }
  // Error state - no user data with improved design
  if (!user) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8 lg:py-12">
          <div className="w-full max-w-4xl mx-auto">
            <Card className="border-destructive/20">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="h-6 w-6 text-destructive" />
                  <CardTitle className="text-destructive">
                    {t('error.title')}
                  </CardTitle>
                </div>
                <CardDescription>{t('error.description')}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-col sm:flex-row gap-3">
                  <Button
                    onClick={() => {
                      if (typeof window !== 'undefined') {
                        window.location.reload();
                      }
                    }}
                  >
                    {t('error.refreshPage')}
                  </Button>
                  <Button variant="outline">{t('error.goToDashboard')}</Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }
  // Contractor onboarding flow with improved design
  if (isContractor && !onboardingCompleted) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8 lg:py-12">
          <div className="w-full max-w-5xl mx-auto">
            {/* Header Section */}
            <div className="mb-8 text-center">
              <h1 className="text-3xl font-bold text-foreground mb-2">
                {t('contractor.onboarding.title')}
              </h1>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                {t('contractor.onboarding.description')}
              </p>
            </div>

            {/* Onboarding Form */}
            <ContractorOnboarding
              onSubmit={handleSubmit}
              onSkipToCompletion={handleSkipToCompletion}
            />

            {/* Loading overlay during submission */}
            {completeContractorOnboarding.isPending && (
              <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
                <Card className="w-auto">
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                      <div>
                        <p className="font-medium text-foreground">
                          {t('contractor.onboarding.submitting')}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {t('contractor.onboarding.submittingDescription')}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  } // Contractor with completed onboarding - improved responsive design
  if (isContractor && onboardingCompleted) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8 lg:py-12">
          <div className="w-full max-w-6xl mx-auto space-y-8">
            {/* Profile Summary Card with improved responsive design */}
            <Card className="overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                      <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <CardTitle className="text-xl font-semibold">
                        {t('contractor.completed.title')}
                      </CardTitle>
                      <CardDescription className="text-base">
                        {t('contractor.completed.description')}
                      </CardDescription>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Profile Information */}
                  <div className="space-y-4">
                    <h3 className="font-semibold text-base text-muted-foreground border-b pb-2">
                      {t('contractor.completed.profileInformation')}
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm text-muted-foreground">
                          {t('contractor.completed.fullName')}
                        </p>
                        <p className="font-medium text-lg">
                          {user.profile?.name}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">
                          {t('contractor.completed.emailAddress')}
                        </p>
                        <p className="font-medium">{user.profile?.email}</p>
                      </div>
                      {user.profile?.phone_number && (
                        <div>
                          <p className="text-sm text-muted-foreground">
                            {t('contractor.completed.phoneNumber')}
                          </p>
                          <p className="font-medium">
                            {user.profile.phone_number}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Role and Status */}
                  <div className="space-y-4">
                    <h3 className="font-semibold text-base text-muted-foreground border-b pb-2">
                      {t('contractor.completed.roleAndStatus')}
                    </h3>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                        <UserCheck className="h-5 w-5 text-blue-600" />
                        <div>
                          <span className="font-medium">
                            {t('contractor.completed.contractor')}
                          </span>
                          <p className="text-sm text-muted-foreground">
                            {t('contractor.completed.verifiedRole')}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <div>
                          <span className="font-medium text-green-700 dark:text-green-400">
                            {t('contractor.completed.registrationComplete')}
                          </span>
                          <p className="text-sm text-green-600 dark:text-green-500">
                            {t('contractor.completed.allRequirementsFulfilled')}
                          </p>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                          {t('contractor.completed.joined')}
                        </h4>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-green-600" />
                          <span className="text-sm">
                            {formatDate(user.created_at)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action buttons with improved responsive layout */}
                <div className="flex flex-col sm:flex-row gap-3 pt-6 mt-6 border-t">
                  <Button
                    className="sm:flex-1 max-w-xs"
                    size="lg"
                    onClick={() => router.push('/projects')}
                  >
                    {t('contractor.completed.goToDashboard')}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => router.push('/settings')}
                    className="sm:flex-1 max-w-xs"
                    size="lg"
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    {t('contractor.completed.accountSettings')}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Contractor Details with loading state */}
            {contractorDataLoading ? (
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-center">
                    <div className="flex items-center space-x-3">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                      <p className="text-sm text-muted-foreground">
                        {t('contractor.completed.loadingContractorDetails')}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : contractorData ? (
              <ContractorDetails contractorData={contractorData} />
            ) : (
              <IncompleteProfileCard
                userRole={userRole || undefined}
                isContractor={isContractor}
                onboardingCompleted={onboardingCompleted}
                hasContractorData={false}
                customMessage={t('contractor.completed.unableToLoadDetails')}
              />
            )}
          </div>
        </div>
      </div>
    );
  }
  // JKR admin onboarding flow - similar to contractor onboarding
  if (isJKR && !onboardingCompleted) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8 lg:py-12">
          <div className="w-full max-w-5xl mx-auto">
            {/* Admin Onboarding Form */}
            <AdminOnboarding onComplete={() => forceProfileRefresh()} />

            {/* Loading overlay during submission */}
            {completeAdminOnboarding.isPending && (
              <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
                <Card className="w-auto">
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                      <div>
                        <p className="font-medium text-foreground">
                          Completing your administrator setup...
                        </p>
                        <p className="text-sm text-muted-foreground">
                          This may take a few moments
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }
  // JKR user - enhanced responsive design (completed onboarding)
  if (isJKR) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8 lg:py-12">
          <div className="w-full max-w-5xl mx-auto space-y-8">
            {/* Header Section */}
            <div className="text-center">
              <h1 className="text-3xl font-bold text-foreground mb-2">
                {t('jkr.title')}
              </h1>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                {t('jkr.description')}
              </p>
            </div>

            {/* Profile Summary Card */}
            <Card className="overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-950/20 dark:to-cyan-950/20">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                      <Building className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <CardTitle className="text-xl font-semibold">
                        {t('jkr.administrator')}
                      </CardTitle>
                      <CardDescription className="text-base">
                        {t('jkr.systemAdministrator')}
                      </CardDescription>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Profile Information */}
                  <div className="space-y-4">
                    <h3 className="font-semibold text-base text-muted-foreground border-b pb-2">
                      {t('jkr.profileInformation')}
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm text-muted-foreground">
                          {t('jkr.fullName')}
                        </p>
                        <p className="font-medium text-lg">
                          {user.profile?.name}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">
                          {t('jkr.emailAddress')}
                        </p>
                        <p className="font-medium">{user.profile?.email}</p>
                      </div>
                      {user.profile?.phone_number && (
                        <div>
                          <p className="text-sm text-muted-foreground">
                            {t('jkr.phoneNumber')}
                          </p>
                          <p className="font-medium">
                            {user.profile.phone_number}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Role and Status */}
                  <div className="space-y-4">
                    <h3 className="font-semibold text-base text-muted-foreground border-b pb-2">
                      {t('jkr.roleAndStatus')}
                    </h3>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                        <Building className="h-5 w-5 text-blue-600" />
                        <div>
                          <span className="font-medium text-blue-700 dark:text-blue-400">
                            {t('jkr.administrator')}
                          </span>
                          <p className="text-sm text-blue-600 dark:text-blue-500">
                            {t('jkr.fullAccess')}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <div>
                          <span className="font-medium">
                            {t('jkr.systemManagement')}
                          </span>
                          <p className="text-sm text-muted-foreground">
                            {t('jkr.description2')}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }
  // Client user - enhanced responsive design
  if (isClient) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8 lg:py-12">
          <div className="w-full max-w-5xl mx-auto space-y-8">
            {/* Header Section */}
            <div className="text-center">
              <h1 className="text-3xl font-bold text-foreground mb-2">
                {t('client.title')}
              </h1>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                {t('client.description')}
              </p>
            </div>

            {/* Profile Summary Card */}
            <Card className="overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-green-50 to-teal-50 dark:from-green-950/20 dark:to-teal-950/20">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                      <Building className="h-8 w-8 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <CardTitle className="text-xl font-semibold">
                        {t('client.client')}
                      </CardTitle>
                      <CardDescription className="text-base">
                        {t('client.projectManagement')}
                      </CardDescription>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Profile Information */}
                  <div className="space-y-4">
                    <h3 className="font-semibold text-base text-muted-foreground border-b pb-2">
                      {t('client.profileInformation')}
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm text-muted-foreground">
                          {t('client.fullName')}
                        </p>
                        <p className="font-medium text-lg">
                          {user.profile?.name}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">
                          {t('client.emailAddress')}
                        </p>
                        <p className="font-medium">{user.profile?.email}</p>
                      </div>
                      {user.profile?.phone_number && (
                        <div>
                          <p className="text-sm text-muted-foreground">
                            {t('client.phoneNumber')}
                          </p>
                          <p className="font-medium">
                            {user.profile.phone_number}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Role and Status */}
                  <div className="space-y-4">
                    <h3 className="font-semibold text-base text-muted-foreground border-b pb-2">
                      {t('client.roleAndStatus')}
                    </h3>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                        <Building className="h-5 w-5 text-green-600" />
                        <div>
                          <span className="font-medium text-green-700 dark:text-green-400">
                            {t('client.buildingClient')}
                          </span>
                          <p className="text-sm text-green-600 dark:text-green-500">
                            {t('client.assetOwner')}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <div>
                          <span className="font-medium">
                            {t('client.activeStatus')}
                          </span>
                          <p className="text-sm text-muted-foreground">
                            {t('client.accountVerified')}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action buttons with improved responsive layout */}
                <div className="flex flex-col sm:flex-row gap-3 pt-6 mt-6 border-t">
                  <Button
                    className="sm:flex-1 max-w-xs"
                    size="lg"
                    onClick={() => router.push('/projects')}
                  >
                    {t('client.goToDashboard')}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => router.push('/settings')}
                    className="sm:flex-1 max-w-xs"
                    size="lg"
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    {t('client.accountSettings')}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Client Management Tools Card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {t('client.assetManagement')}
                </CardTitle>
                <CardDescription>
                  {t('client.assetManagementDescription')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  <Button
                    variant="outline"
                    className="h-auto p-4 flex-col space-y-2"
                    onClick={() => router.push('/buildings')}
                  >
                    <Building className="h-6 w-6" />
                    <span className="text-sm font-medium">
                      {t('client.myBuildings')}
                    </span>
                  </Button>
                  <Button
                    variant="outline"
                    className="h-auto p-4 flex-col space-y-2"
                    onClick={() => router.push('/lifts')}
                  >
                    <Settings className="h-6 w-6" />
                    <span className="text-sm font-medium">
                      {t('client.liftStatus')}
                    </span>
                  </Button>
                  <Button
                    variant="outline"
                    className="h-auto p-4 flex-col space-y-2"
                    onClick={() => router.push('/maintenance')}
                  >
                    <UserCheck className="h-6 w-6" />
                    <span className="text-sm font-medium">
                      {t('client.maintenance')}
                    </span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }
  // Fallback for unknown role or missing profile data with improved design
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8 lg:py-12">
        <div className="w-full max-w-4xl mx-auto">
          <IncompleteProfileCard
            userRole={userRole || undefined}
            isContractor={isContractor}
            onboardingCompleted={onboardingCompleted}
            hasContractorData={!!contractorData}
            customMessage={t('fallback.message')}
          />
        </div>
      </div>
    </div>
  );
}
