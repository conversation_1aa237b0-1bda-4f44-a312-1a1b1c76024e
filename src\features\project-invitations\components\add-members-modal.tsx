'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useInviteUser } from '@/features/projects/hooks/use-project-invitations';
import { useProjectContext } from '@/providers/project-context';
import type { Database } from '@/types/database';
import {
  AlertCircle,
  CheckCircle2,
  Mail,
  Plus,
  Trash2,
  XCircle,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';

interface InvitationMember {
  email: string;
  role: Database['public']['Enums']['project_role'];
}

interface MemberStatus {
  status: 'success' | 'error' | 'user_not_found' | 'profile_incomplete' | null;
  message: string;
}

interface AddMembersModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

/**
 * Modal component for adding new members to a project
 * Uses the new invitation system with status tracking
 */
export function AddMembersModal({
  open,
  onOpenChange,
  onSuccess,
}: AddMembersModalProps) {
  const t = useTranslations('pages.members');
  const { selectedProjectId } = useProjectContext();
  const [members, setMembers] = useState<InvitationMember[]>([
    { email: '', role: 'technician' as const },
  ]);
  const [memberStatuses, setMemberStatuses] = useState<
    Record<number, MemberStatus>
  >({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const inviteUser = useInviteUser();

  // Reset form when modal opens/closes
  useEffect(() => {
    if (!open) {
      setMembers([{ email: '', role: 'technician' as const }]);
      setMemberStatuses({});
      setIsSubmitting(false);
    }
  }, [open]);

  const addMemberRow = () => {
    setMembers([...members, { email: '', role: 'technician' as const }]);
  };

  const removeMemberRow = (index: number) => {
    if (members.length > 1) {
      const updatedMembers = members.filter((_, i) => i !== index);
      setMembers(updatedMembers);

      // Clear status for removed row
      const newStatuses = { ...memberStatuses };
      delete newStatuses[index];
      // Reindex remaining statuses
      const reindexedStatuses: Record<number, MemberStatus> = {};
      Object.entries(newStatuses).forEach(([key, value]) => {
        const oldIndex = parseInt(key);
        const newIndex = oldIndex > index ? oldIndex - 1 : oldIndex;
        reindexedStatuses[newIndex] = value;
      });
      setMemberStatuses(reindexedStatuses);
    }
  };

  const updateMember = (
    index: number,
    field: 'email' | 'role',
    value: string,
  ) => {
    const updatedMembers = [...members];
    if (field === 'email') {
      updatedMembers[index].email = value;
    } else if (field === 'role') {
      updatedMembers[index].role =
        value as Database['public']['Enums']['project_role'];
    }
    setMembers(updatedMembers);

    // Clear status when email changes
    if (field === 'email') {
      const newStatuses = { ...memberStatuses };
      delete newStatuses[index];
      setMemberStatuses(newStatuses);
    }
  };

  const handleAddMembers = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedProjectId) {
      return;
    }

    // Validate that all members have email
    const validMembers = members.filter((member) => member.email.trim());

    if (validMembers.length === 0) {
      return;
    }

    setIsSubmitting(true);
    let successCount = 0;
    let errorCount = 0;

    // Send invitations sequentially to better handle results
    for (let i = 0; i < validMembers.length; i++) {
      const member = validMembers[i];
      const originalIndex = members.findIndex(
        (m) => m.email === member.email && m.role === member.role,
      );

      try {
        await inviteUser.mutateAsync({
          projectId: selectedProjectId,
          userEmail: member.email,
          role: member.role,
        });

        setMemberStatuses((prev) => ({
          ...prev,
          [originalIndex]: {
            status: 'success',
            message: t('invitation.success', {
              defaultValue: 'Invitation sent successfully',
            }),
          },
        }));
        successCount++;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error';
        const isUserNotFound =
          errorMessage?.includes('User not found') ||
          errorMessage?.includes('not found');
        const isProfileIncomplete =
          errorMessage?.includes('not completed their registration') ||
          errorMessage?.includes('profile setup');

        setMemberStatuses((prev) => ({
          ...prev,
          [originalIndex]: {
            status: isUserNotFound
              ? 'user_not_found'
              : isProfileIncomplete
                ? 'profile_incomplete'
                : 'error',
            message:
              errorMessage ||
              t('invitation.error', {
                defaultValue: 'Failed to send invitation',
              }),
          },
        }));
        errorCount++;
      }
    }

    setIsSubmitting(false);

    // If all successful, close modal and call onSuccess
    if (successCount > 0 && errorCount === 0) {
      setTimeout(() => {
        onOpenChange(false);
        onSuccess?.();
      }, 1500);
    }
  };

  const getStatusIcon = (status: string | null) => {
    switch (status) {
      case 'success':
        return <CheckCircle2 className="h-4 w-4 text-green-600" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'user_not_found':
        return <AlertCircle className="h-4 w-4 text-yellow-600" />;
      case 'profile_incomplete':
        return <AlertCircle className="h-4 w-4 text-orange-600" />;
      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            {t('addMembers.title', { defaultValue: 'Add Team Members' })}
          </DialogTitle>
          <DialogDescription>
            {t('addMembers.description', {
              defaultValue:
                'Add existing users to this project. They will be notified and can accept or decline the invitation.',
            })}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleAddMembers} className="space-y-4">
          <div className="space-y-4">
            {members.map((member, index) => (
              <div key={index} className="space-y-3 p-4 border rounded-lg">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">
                    {t('member', { defaultValue: 'Member' })} {index + 1}
                  </Label>
                  {members.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeMemberRow(index)}
                      className="h-8 w-8 p-0"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="space-y-2">
                    <Label htmlFor={`email-${index}`}>
                      {t('email', { defaultValue: 'Email' })}
                    </Label>
                    <Input
                      id={`email-${index}`}
                      type="email"
                      placeholder="<EMAIL>"
                      value={member.email}
                      onChange={(e) =>
                        updateMember(index, 'email', e.target.value)
                      }
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`role-${index}`}>
                      {t('role', { defaultValue: 'Role' })}
                    </Label>
                    <Select
                      value={member.role}
                      onValueChange={(value) =>
                        updateMember(index, 'role', value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="technician">
                          {t('roles.technician', {
                            defaultValue: 'Technician',
                          })}
                        </SelectItem>
                        <SelectItem value="competent_person">
                          {t('roles.competentPerson', {
                            defaultValue: 'Competent Person',
                          })}
                        </SelectItem>
                        <SelectItem value="admin">
                          {t('roles.admin', { defaultValue: 'Admin' })}
                        </SelectItem>
                        <SelectItem value="viewer">
                          {t('roles.viewer', { defaultValue: 'Viewer' })}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Status Display */}
                {memberStatuses[index] && (
                  <div className="flex items-center gap-2 text-sm">
                    {getStatusIcon(memberStatuses[index].status)}
                    <span
                      className={
                        memberStatuses[index].status === 'success'
                          ? 'text-green-600'
                          : memberStatuses[index].status === 'user_not_found'
                            ? 'text-yellow-600'
                            : memberStatuses[index].status ===
                                'profile_incomplete'
                              ? 'text-orange-600'
                              : 'text-red-600'
                      }
                    >
                      {memberStatuses[index].message}
                    </span>
                  </div>
                )}
              </div>
            ))}
          </div>

          <Button
            type="button"
            variant="outline"
            onClick={addMemberRow}
            className="w-full flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            {t('addAnother', { defaultValue: 'Add Another Member' })}
          </Button>

          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              {t('cancel', { defaultValue: 'Cancel' })}
            </Button>
            <Button type="submit" disabled={isSubmitting || !selectedProjectId}>
              {isSubmitting
                ? t('sending', { defaultValue: 'Sending...' })
                : t('sendInvitations', { defaultValue: 'Send Invitations' })}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
