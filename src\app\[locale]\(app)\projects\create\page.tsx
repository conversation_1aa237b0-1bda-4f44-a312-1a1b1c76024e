'use client';

import {
  B<PERSON><PERSON>rumb,
  Bread<PERSON>rumb<PERSON><PERSON>,
  Bread<PERSON>rumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ProjectFormData,
  ProjectFormMultiStep,
  useCreateProject,
} from '@/features/projects';
import { usePermissions } from '@/hooks/use-permissions';
import { useToast } from '@/hooks/use-toast';
import { Building2, ShieldAlert } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

const CreateProjectPage = () => {
  const router = useRouter();
  const t = useTranslations('pages.projects');
  const { isContractor, isJKR, isLoading } = usePermissions();

  const createProjectMutation = useCreateProject();
  const { toast } = useToast();

  // Show loading state while checking permissions
  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  // Prevent admin users from accessing project creation
  if (isJKR) {
    return (
      <div className="container mx-auto py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-full bg-amber-100">
                  <ShieldAlert className="h-6 w-6 text-amber-600" />
                </div>
                <div>
                  <CardTitle>{t('create.restrictions.adminTitle')}</CardTitle>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                {t('create.restrictions.adminMessage')}
              </p>
              <p className="text-sm text-muted-foreground">
                {t('create.restrictions.adminHelp')}
              </p>
              <div className="pt-4">
                <Link
                  href="/projects"
                  className="inline-flex items-center gap-2 text-primary hover:text-primary/80 transition-colors"
                >
                  ← {t('create.restrictions.backToProjects')}
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Only allow contractors to proceed
  if (!isContractor) {
    return (
      <div className="container mx-auto py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-full bg-red-100">
                  <ShieldAlert className="h-6 w-6 text-red-600" />
                </div>
                <div>
                  <CardTitle>
                    {t('create.restrictions.accessDeniedTitle')}
                  </CardTitle>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                {t('create.restrictions.accessDeniedMessage')}
              </p>
              <div className="pt-4">
                <Link
                  href="/projects"
                  className="inline-flex items-center gap-2 text-primary hover:text-primary/80 transition-colors"
                >
                  ← {t('create.restrictions.backToProjects')}
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const handleSubmit = async (data: ProjectFormData) => {
    try {
      const project = await createProjectMutation.mutateAsync(data);
      toast({
        title: t('create.successTitle'),
        description: t('create.successDescription', { name: project.name }),
        variant: 'default',
      });
      router.push('/projects');
      return { id: project.id, code: project.code ?? '' };
    } catch (error) {
      toast({
        title: t('create.errorTitle'),
        description:
          error instanceof Error ? error.message : t('create.errorGeneric'),
        variant: 'destructive',
      });
      throw error;
    }
  };

  const handleCancel = () => {
    router.push('/projects');
  };

  return (
    <div className="container mx-auto py-6 max-w-4xl">
      <div className="space-y-6">
        {/* Breadcrumb */}
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/projects" className="flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  {t('title')}
                </Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{t('create.title')}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        {/* Form */}
        <ProjectFormMultiStep
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={createProjectMutation.isPending}
        />

        {/* Development Info */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 className="text-sm font-medium text-yellow-800 mb-2">
              Development Info
            </h3>
            <div className="text-xs text-yellow-700 space-y-1">
              <p>• Make sure Supabase is running locally</p>
              <p>• Files will be uploaded to OBS storage</p>
              <p>• Check browser console for detailed logs</p>
              <p>
                • Mutation status:{' '}
                {createProjectMutation.isPending ? 'Loading...' : 'Ready'}
              </p>
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        .required::after {
          content: ' *';
          color: red;
        }
      `}</style>
    </div>
  );
};

export default CreateProjectPage;
