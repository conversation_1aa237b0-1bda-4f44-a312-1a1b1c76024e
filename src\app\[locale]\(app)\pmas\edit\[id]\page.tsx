'use client';

import { But<PERSON> } from '@/components/ui/button';
import { PmaForm } from '@/features/pma-management/components/pma-form';
import {
  usePMACertificate,
  useUpdatePMACertificate,
} from '@/features/pma-management/hooks/use-pma-certificates';
import { useProjectContext } from '@/providers/project-context';
import { FileText } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';

// Import the proper types
type PmaEditFormData = {
  pmas: {
    location?: string;
    pmaNumber?: string;
    inspectionDate?: string;
    competentPersonId?: string;
    pmaExpiryDate: string;
    pdfFile?: File;
  }[];
};

export default function EditPma() {
  const { selectedProjectId } = useProjectContext();
  const params = useParams();
  const pmaId = params.id as string;
  const locale = params.locale as string;
  const router = useRouter();
  const t = useTranslations('pmaManagement.pages.edit');
  const tNav = useTranslations('navigation');

  // Fetch the PMA certificate data
  const { data: pma, isLoading: isLoadingPma } = usePMACertificate(pmaId);
  const { mutate, isPending } = useUpdatePMACertificate();

  // Prepare initial form data when PMA data is loaded
  const initialData = pma
    ? {
        pmas: [
          {
            location: pma.location || '',
            pmaNumber: pma.pma_number || '',
            inspectionDate:
              typeof pma.inspection_date === 'string'
                ? pma.inspection_date
                : '',
            competentPersonId: pma.competent_person_id || '',
            pmaExpiryDate: pma.expiry_date || '',
            // No pdfFile here as we're not uploading a new file in edit mode
          },
        ],
      }
    : undefined;

  const handleSubmit = async (data: PmaEditFormData) => {
    console.log('Form submitted with data:', data);
    console.log('PDF File:', data.pmas[0].pdfFile);

    if (!selectedProjectId || !pma) {
      console.error('Project ID or PMA is not available.');
      return;
    }

    try {
      // Check if a new file is being uploaded
      let fileUrl = pma.file_url;

      if (data.pmas[0].pdfFile) {
        console.log('Uploading new file...');
        // Import the upload function
        const { uploadToOBS } = await import('@/lib/obs-upload');

        // Upload the new file
        fileUrl = await uploadToOBS({
          file: data.pmas[0].pdfFile,
          folder: `projects/${selectedProjectId}/pma-certificates`,
        });
        console.log('File uploaded successfully:', fileUrl);
      } else {
        console.log(
          'No new file to upload, keeping existing file URL:',
          fileUrl,
        );
      }

      // Update both expiry date and file URL if a new file was uploaded
      mutate(
        {
          id: pmaId,
          updates: {
            expiry_date: data.pmas[0].pmaExpiryDate,
            file_url: fileUrl,
          },
        },
        {
          onSuccess: () => {
            router.push(`/${locale}/pmas`);
          },
          onError: (error) => {
            console.error('Error updating PMA:', error);
          },
        },
      );
    } catch (error) {
      console.error('Error uploading file:', error);
    }
  };

  return (
    <div className="bg-gradient-to-br from-slate-50 via-white to-slate-50/50">
      {/* Enhanced Header with Breadcrumbs */}
      <div className="sticky top-0 z-40 bg-white/80 backdrop-blur-lg border-b border-slate-200/60">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Breadcrumbs */}
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Link
                href={`/${locale}/pmas`}
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                <div className="flex items-center space-x-2">
                  <FileText className="h-4 w-4" />
                  <span>{tNav('pmas')}</span>
                </div>
              </Link>
              <span className="text-muted-foreground">/</span>
              <span className="text-primary font-medium">
                {t('breadcrumb')}
              </span>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={() => router.push(`/${locale}/pmas`)}
              >
                {t('cancel')}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
              {t('title')}
            </h1>
            <p className="mt-2 text-lg text-muted-foreground">
              {t('description')}
            </p>
          </div>

          {/* Form */}
          {isLoadingPma ? (
            <div className="flex justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
            </div>
          ) : (
            <PmaForm
              onSubmit={handleSubmit}
              initialData={initialData}
              isLoading={isPending}
              isEditMode={true}
              disabledFields={[
                'location',
                'pmaNumber',
                'inspectionDate',
                'competentPersonId',
              ]}
            />
          )}
        </div>
      </div>
    </div>
  );
}
