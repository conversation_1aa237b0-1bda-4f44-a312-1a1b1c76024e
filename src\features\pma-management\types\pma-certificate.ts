import type { BaseTableRow } from '@/components/table-section';

export type PmaDbStatus = 'valid' | 'validating' | 'invalid';

export interface PMACertificate extends BaseTableRow {
  id: string;
  project_id: string | null;
  pma_number: string | null;
  expiry_date: string;
  status: string;
  file_url?: string | null;
  competent_person_id?: string | null;
  location?: string | null;
  state?: string | null;
  created_at?: string | null;
  updated_at?: string | null;
  deleted_at?: string | null;
  deleted_by?: string | null;
}
