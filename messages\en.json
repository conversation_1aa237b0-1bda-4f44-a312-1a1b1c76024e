{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "submit": "Submit", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "close": "Close", "yes": "Yes", "no": "No", "notSet": "Not set", "unknown": "Unknown", "firstTime": "First time", "justNow": "Just now", "new": "New", "viewDetails": "View Details", "export": "Export", "settings": "Settings", "projects": "Projects", "currentProject": "Current Project", "noRecordsFound": "No records found", "noRecordsAvailable": "No records available", "tryAdjustingFilters": "Try adjusting your filters or create a new record", "filters": "Filters", "filterOptions": "Filter Options", "clearAll": "Clear all", "columns": "Columns", "actions": "Actions", "showingEntries": "Showing {count} of {total} entries", "recordsFound": "{count} records found", "noRecords": "No records available"}, "components": {"removeMemberDialog": {"title": "Remove Team Member", "description": "Are you sure you want to remove this member from the project?", "warning": "This member will lose access to the project but can be restored later by an admin.", "cancel": "Cancel", "confirm": "Remove Member", "successMessage": "{member<PERSON><PERSON>} has been removed from the project", "errorMessage": "Failed to remove member: {error}"}}, "navigation": {"navigation": "Navigation", "professionalEdition": "Contractor Portal", "adminPanel": "Admin Portal", "portalTitle": {"contractor": "Contractor Portal", "admin": "Admin Portal"}, "signingOut": "Signing out...", "signOut": "Sign Out", "loadingMenu": "Loading menu...", "languages": {"en": "English", "ms": "Bahasa Malaysia"}, "dashboard": "Dashboard", "project": "Project", "projects": "Projects", "lifts": "Lifts", "buildings": "Buildings", "directory": "CP Directory", "dailyLogs": "Daily Logs", "pmas": "PMA Management", "complaints": "<PERSON><PERSON><PERSON><PERSON>", "contractors": "Contractors", "competentPersons": "Competent Persons", "complaintAdmin": "<PERSON><PERSON><PERSON><PERSON>", "clients": "Clients", "blacklist": "Blacklist", "analytics": "Analytics", "reports": "Reports", "users": "Users", "profile": "Profile", "cpList": "CP List", "settings": "Settings", "maintenanceLogs": "Maintenance Logs", "members": "Members", "pmaManagement": "PMA Management", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "descriptions": {"dashboard": "Overview of lift management activities", "projects": "Manage your assigned projects and tasks", "lifts": "Manage lift inventory and status", "buildings": "Manage building information", "contracts": "Maintenance contracts management", "dailyLogs": "Daily monitoring and maintenance logs", "pmas": "Periodic Maintenance Agreements", "complaints": "Customer complaints and issues", "contractors": "Manage contractor registrations and certifications", "competentPersons": "Manage competent persons in the system", "userManagement": "Manage user accounts and permissions", "clients": "Manage client information", "blacklist": "Blacklisted contractors management", "analytics": "Performance analytics and insights", "reports": "Generate and view system reports", "users": "System user management", "profile": "Manage your profile settings", "cpList": "View and manage competent persons in your company", "settings": "System configuration settings"}}, "pmaManagement": {"title": "Add PMA Details", "subtitle": "Enter the details for each PMA assignment", "newLogEntry": "New Log Entry", "searchPlaceholder": "Search PMA logs...", "filter": "Filter", "export": "Export", "addMorePmaEntries": "Add more PMA entries above", "form": {"pmaEntry": "PMA Entry #{number}", "remove": "Remove", "pmaInformation": "PMA Information", "pmaInformationDescription": "Complete all fields to add a new PMA assignment", "addedPmaEntries": "Added PMA Entries", "addedPmaEntriesDescription": "Review your PMA entries before submission", "customer": "Customer", "customerPlaceholder": "Enter client name", "agency": "Agency", "agencyPlaceholder": "Enter agency name", "location": "Location", "locationPlaceholder": "Enter location details", "pmaNumber": "PMA No.", "pmaNumberPlaceholder": "Enter PMA number", "competentPerson": "Competent Person", "competentPersonPlaceholder": "Select competent person", "noCompetentPersonsAvailable": "No competent persons available", "pmaExpiryDate": "PMA Expiry Date", "pmaExpiryDatePlaceholder": "Select PMA expiry date", "lifLocation": "LIF Location", "lifLocationPlaceholder": "Enter detailed LIF location (e.g., Building A, Level 3, Lift Bank 1)", "pmaCertificateFile": "PMA Certificate File", "pmaCertificateFileNote": "(PDF only, max 10MB)", "type": "Brand", "supervisor": "Supervisor", "supervisorPlaceholder": "Enter supervisor name", "inspectionDate": "Inspection Date", "pdfUpload": "PMA Certificate (PDF)", "pdfUploadDescription": "Upload the PMA certificate document (PDF format, max 10MB)", "assignCp": "Assign CP", "ready": "Ready", "edit": "Edit", "delete": "Delete"}, "validation": {"atLeastOnePmaRequired": "At least one PMA is required.", "locationRequired": "Location is required", "pmaNumberRequired": "PMA number is required", "inspectionDateRequired": "Inspection date is required"}, "actions": {"saveDraft": "Save Draft", "addAnotherPma": "Add PMA", "cancel": "Cancel", "submitPma": "Add PMA Entries", "saveChanges": "Save Changes"}, "statistics": {"activePmas": "Active PMAs", "completed": "Completed", "expiringSoon": "Expiring Soon", "overdue": "Overdue"}, "activityLogs": {"title": "PMA Activity Logs", "subtitle": "Track and manage all PMA entries and their current status"}, "table": {"pmaNumber": "PMA Number", "customer": "Customer", "project": "Project", "contractor": "Contractor", "createdDate": "Created Date", "expiryDate": "Expiry Date", "status": "Status", "actions": "Actions", "totalRepairCost": "Total Repair Cost", "totalRepairTime": "Total Repair Time", "loading": "Loading...", "empty": "No PMA logs found", "noRecordsFound": "No records found", "tryAdjustingFilters": "Try adjusting your filters or add a new PMA entry"}, "status": {"active": "Active", "expiring_soon": "Expiring Soon", "expired": "Expired", "completed": "Completed"}, "pagination": {"showing": "Showing {count} of {total} entries", "previous": "Previous", "next": "Next"}, "detail": {"title": "PMA Certificate Details", "dateCreated": "Date Created", "lastUpdated": "Last Updated", "downloadCertificate": "Download Certificate", "noCertificateUploaded": "No certificate uploaded"}, "filters": {"title": "Filters", "searchPlaceholder": "Search by PMA number, location, or state...", "optionsTitle": "Filter Options", "clearAll": "Clear all"}, "searchFilterTitle": "Search & Filter PMA Logs", "searchFilterDescription": "Use the filters below to find and manage PMA entries quickly.", "pages": {"add": {"title": "Add New PMA Entry", "description": "Add one or more PMA entries for your project.", "breadcrumb": "Add New PMA", "cancel": "Cancel"}, "edit": {"title": "Edit PMA Entry", "description": "Update the expiry date for this PMA entry.", "breadcrumb": "Edit PMA", "cancel": "Cancel"}}}, "dashboard": {"title": "Dashboard", "welcomeBack": "Welcome back, {name}!", "welcomeMessage": "Welcome, {name}!", "welcomeDescription": "Here's what's happening with your project today.", "loading": "Loading dashboard...", "loadingProject": "Loading project dashboard...", "errorLoading": "Error loading profile: {error}", "profile": {"title": "Your Profile", "email": "Email", "name": "Name", "role": "Role", "phone": "Phone", "memberSince": "Member Since", "lastLogin": "Last Login"}, "quickActions": {"title": "Quick Actions", "editProfile": "Edit Profile", "settings": "Settings"}, "recentActivity": {"title": "Recent Activity", "noActivity": "No recent activity to display."}, "statistics": {"title": "Account Statistics", "loginStatus": "Login Status", "accountType": "Account Type", "lastLogin": "Last Login", "active": "Active", "standard": "Standard"}, "widgets": {"pma": {"title": "PMA Certificates", "viewAll": "View All", "loading": "Loading PMA certificate data...", "certificateStatus": "Certificate Status", "active": "Active", "expiringSoon": "Expiring Soon", "expired": "Expired", "noCertificateData": "No certificate data available", "noCertificateMessage": "There are no certificates in the system yet.", "addCertificate": "Add Certificate", "total": "Total", "noProblematicLifts": "No Problematic Lifts", "allLiftsNormal": "All your lifts are operating normally - great work!", "mostProblematicLifts": "Most Problematic Lifts"}, "maintenance": {"title": "Maintenance Logs", "viewAll": "View All", "loading": "Loading maintenance data...", "recentLogs": "Recent Maintenance Logs", "noLogs": "There are no maintenance logs in the system yet.", "completedToday": "Completed Today", "pendingTasks": "Pending Tasks", "noMaintenanceData": "No Maintenance Data", "loadingStatus": "Loading status data...", "maintenanceStatus": "Maintenance Status", "addMaintenanceLog": "Add Maintenance Log", "loadingRecent": "Loading recent maintenance logs...", "recentMaintenance": "Recent Maintenance", "getStartedMessage": "Get started by creating your first maintenance log.", "status": {"completed": "Completed", "pending": "Pending", "overdue": "Overdue", "inProgress": "In Progress"}, "total": "Total"}, "complaints": {"title": "<PERSON><PERSON><PERSON><PERSON>", "viewAll": "View All", "loading": "Loading complaints data...", "recentComplaints": "Recent Complaints", "noComplaints": "No Complaints Data", "noComplaintsMessage": "There are no complaints in the system yet.", "noComplaintsFound": "No Complaints Found", "noComplaintsReported": "No Complaints Reported", "noComplaintsInPeriod": "There are no complaints in the selected time period. This could be a good sign!", "openComplaints": "Open Complaints", "resolvedComplaints": "Resolved <PERSON>mp<PERSON><PERSON>", "createComplaint": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "statusDistribution": "Complaint Status Distribution", "loadingRecent": "Loading recent complaints...", "total": "Total", "getStartedMessage": "Get started by creating your first complaint.", "trendAnalysisTitle": "Complaints Trend Analysis", "analytics": "ANALYTICS", "status": {"new": "New", "inProgress": "In Progress", "resolved": "Resolved", "closed": "Closed"}, "trendAnalysis": "Complaint Trend Analysis", "timeRange": {"6months": "Last 6 Months", "12months": "Last 12 Months"}, "chartDescription": "Track complaint trends over time to identify patterns and improve response times.", "chartOverview": {"6months": "Overview of total complaints for the last 6 months (Jan-Jun 2024)", "12months": "Overview of total complaints over the past 12 months (Jan 2023-Dec 2024)"}, "months": {"January": "January", "February": "February", "March": "March", "April": "April", "May": "May", "June": "June", "July": "July", "August": "August", "September": "September", "October": "October", "November": "November", "December": "December"}, "addLog": "Add Maintenance Log", "noData": "No data", "noChange": "No change", "thisPeriod": "this period"}, "stats": {"expiringSoon": "Expiring Soon", "expiringSoonDescription": "Certificates expiring within the next 30 days", "dailyCompletion": "Daily Completion", "dailyCompletionDescription": "Completed maintenance logs vs total certificates", "openComplaints": "Open Complaints", "openComplaintsDescription": "Complaints requiring attention", "overdueTasksDescription": "Maintenance tasks completed after 5 PM"}}}, "pages": {"projects": {"title": "Project", "description": "Manage your assigned projects and tasks", "noProjects": "No Projects Found", "noProjectsDescription": "You don't have any assigned projects yet.", "createFirst": "Create Your First Project", "stats": {"total": "Total Projects", "active": "Active", "pending": "Pending", "completed": "Completed"}, "create": {"title": "Create New Project", "description": "Fill in the details below to create a new project", "successTitle": "Project created successfully", "successDescription": "Your new project has been created and is ready to use", "errorTitle": "Failed to create project", "errorGeneric": "There was an error creating the project. Please try again", "restrictions": {"adminTitle": "Access Restricted", "adminMessage": "Administrators cannot create new projects. As an admin, you can only be invited to existing projects by contractors.", "adminHelp": "If you need to access a project, please contact the project owner to send you an invitation.", "accessDeniedTitle": "Access Denied", "accessDeniedMessage": "You do not have permission to create projects.", "backToProjects": "Back to Projects"}, "form": {"title": "Project Information", "description": "Enter the basic information about your project", "multiStepTitle": "Create New Project", "multiStepDescription": "Set up your project with all necessary details and documentation", "requiredFieldsNote": "All fields marked with", "areRequired": "are required", "sections": {"projectDetails": "Project Details", "pmaInformation": "PMA Information", "pmaDescription": "Provide the Project Management Authority (PMA) details for this project."}, "fields": {"name": {"label": "Project Name", "placeholder": "Enter project name"}, "code": {"label": "Quotation Number", "placeholder": "e.g., QUO-2025-001"}, "agency": {"label": "Agency", "placeholder": "Select agency or type to create new", "searchPlaceholder": "Search agencies...", "createNew": "Create new agency", "emptyMessage": "No agencies found"}, "state": {"label": "State", "placeholder": "Select state"}, "location": {"label": "Location", "placeholder": "Enter project location"}, "personInCharge": {"label": "Person in Charge", "placeholder": "Select person in charge", "noStateSelected": "Please select a state first to see available JKR personnel for that region.", "noPersonnelFound": "No JKR personnel found for the selected state", "personnelFoundNote": "Showing JKR personnel for", "searchPlaceholder": "Search by name, email, or role...", "emptyMessage": "No JKR personnel found."}, "startDate": {"label": "Start Date"}, "endDate": {"label": "End Date"}, "status": {"label": "Status", "placeholder": "Select project status", "options": {"pending": "Pending", "active": "Active", "completed": "Completed", "cancelled": "Cancelled"}}, "description": {"label": "Description", "placeholder": "Enter project description (optional)"}, "pmaNumber": {"label": "PMA Number", "placeholder": "Enter PMA number"}, "pmaExpiry": {"label": "PMA Expiry Date"}, "lifLocation": {"label": "LIF Location", "placeholder": "Enter detailed LIF location (e.g., Building A, Level 3, Lift Bank 1)"}, "competentPerson": {"label": "Competent Person", "placeholder": "Select competent person", "noCompetentPersonsAvailable": "No competent persons available", "noCompetentPersonsNote": "No competent persons found. Please add competent persons to your contractor profile first."}, "pmaCertificateFile": {"label": "PMA Certificate File", "note": "(PDF only)", "removeFile": "Remove file", "fileUploaded": "File uploaded successfully"}}, "actions": {"create": "Create Project", "creating": "Creating...", "next": "Next", "previous": "Previous", "cancel": "Cancel", "addAnotherPma": "Add Another PMA", "remove": "Remove", "createProject": "Create Project", "creatingProject": "Creating Project..."}, "pma": {"cardTitle": "PMA {number}", "remove": "Remove"}}, "validation": {"required": "Required fields missing", "requiredFields": "Please fill in all required fields", "dateError": "Invalid date range", "endDateAfterStart": "End date must be after start date", "unsavedChanges": "You have unsaved changes. Are you sure you want to cancel?", "unsavedChangesMessage": "You have unsaved changes that will be lost if you leave this page. Are you sure you want to continue?"}, "success": {"title": "Project created successfully", "description": "Your new project has been created and is ready to use"}, "error": {"title": "Failed to create project", "description": "There was an error creating the project. Please try again"}}, "invitations": {"title": "Pending Invitations", "description": "Review and respond to project invitations.", "noInvitations": "You have no pending project invitations.", "accept": "Accept", "decline": "Decline", "roleLabel": "Role", "projectCode": "Project Code", "location": "Location", "unknownProject": "Unknown Project"}}, "members": {"title": "Project Members", "description": "Manage project team members and their roles", "addMember": "Add Member", "teamMembers": "Team Members", "noMembers": "No team members found", "noMembersDescription": "This project doesn't have any team members yet.", "addFirstMember": "Add your first team member", "addMembers": {"title": "Add Team Members", "description": "Add existing users to this project. They will be notified and can accept or decline the invitation."}, "member": "Member", "email": "Email", "role": "Role", "roles": {"technician": "Technician", "competentPerson": "Competent Person", "admin": "Admin", "viewer": "Viewer"}, "status": {"accepted": "Active", "invited": "Invited", "declined": "Declined"}, "addAnother": "Add Another Member", "cancel": "Cancel", "sendInvitations": "Send Invitations", "sending": "Sending...", "invitation": {"success": "Invitation sent successfully", "error": "Failed to send invitation"}, "modal": {"title": "Add New Members", "description": "Invite new members to join this project", "addAnother": "Add another member", "removeMember": "Remove member", "email": {"label": "Email Address", "placeholder": "Enter member's email address"}, "role": {"label": "Role", "placeholder": "Select a role", "options": {"technician": "Technician"}}, "errors": {"alreadyAdded": "This user is already a member of this project", "userNotFound": "User with this email does not exist in the system", "invalidEmail": "Please enter a valid email address", "duplicateEmail": "This email has already been entered", "addingFailed": "Failed to add this member to the project", "inviteFailed": "Failed to send invitation to {email}: {error}"}, "status": {"success": "Successfully added", "alreadyAdded": "Already a member", "userNotFound": "User not found", "invited": "Invitation sent", "error": "Failed to add"}, "success": {"invited": "Invitation sent successfully to {email}"}, "actions": {"cancel": "Cancel", "add": "Add Members", "adding": "Adding..."}}}, "maintenanceLogs": {"page": {"errorTitle": "Something went wrong", "errorDescription": "We encountered an error while loading your maintenance logs. Please try again.", "tryAgain": "Try Again", "dashboardTitle": "Maintenance Dashboard", "dashboardDescription": "Monitor your equipment status and maintenance activities in real-time", "recordsTitle": "Maintenance Records", "recordsFound": "{count} records found", "noRecords": "No records available", "addRecord": "Add Record", "searchFilterTitle": "Search & Filter", "searchFilterDescription": "Find specific maintenance records", "tableTitle": "Data Table", "tableRecords": "{count} maintenance records", "noData": "No data available", "pageOf": "Page {current} of {total}", "showingResults": "Showing {from} to {to} of {total} results", "cards": {"fullyFunctional": {"title": "Fully Functional", "description": "Equipment running smoothly"}, "partiallyWorking": {"title": "Partially Working", "description": "Needs attention"}, "broken": {"title": "Broken/Issues", "description": "Requires immediate action"}}}, "title": "Maintenance Logs", "description": {"default": "Track and manage all maintenance activities", "withProject": "Comprehensive maintenance records for {projectName}"}, "addLog": "Add Log", "table": {"loading": "Loading...", "empty": "No maintenance logs found", "columns": {"operation_log_type": "Type", "status": "Status", "log_date": "Date", "contractor_name": "Contractor", "person_in_charge_name": "Person in Charge", "description": "Description", "pma_number": "PMA Certificate", "actions": "Actions", "created_by": "Created by"}}, "filters": {"type": {"placeholder": "Select type", "all": "All Types"}, "status": {"placeholder": "Select status", "all": "All Status"}, "dateRange": {"placeholder": "Pick a date range"}, "search": {"placeholder": "Search maintenance logs", "placeholderDetailed": "Search by description, person in charge, phone, contractor, type, status, or PMA number...", "placeholderEnhanced": "Search by description, person, contractor, type, status...", "placeholderCompact": "Search by description, contractor, person in charge..."}}, "cards": {"error": "Failed to load maintenance statistics", "dailyLogs": {"title": "Daily Logs", "description": "Daily maintenance activities"}, "secondSchedule": {"title": "Second Schedule", "description": "Scheduled maintenance"}, "mantrap": {"title": "Mantrap", "description": "Critical mantrap activities"}, "totalLogs": {"title": "Total Logs", "description": "{count} this month", "trend": "{value} vs last month"}, "recentActivity": {"title": "Recent Activity", "description": "Last 7 days"}, "thisMonth": {"title": "This Month", "description": "Current month total"}, "previousMonth": {"title": "Previous Month", "description": "Last month comparison"}, "operationTypeDistribution": "Operation Type Distribution", "weekTrend": "7-Day Activity Trend", "logs": "logs"}, "status": {"fully function": "Fully Function", "broken": "Broken"}, "operationType": {"daily logs": "Daily Logs", "second schedule": "Second Schedule", "mantrap": "Mantrap"}, "pagination": {"pageOf": "Page {current} of {total}", "prev": "Go to previous page", "next": "Go to next page"}, "create": {"title": "Create Maintenance Log", "description": "Record a new maintenance activity for your project.", "breadcrumb": "Create New Log", "loading": "Loading project details...", "cancel": "Cancel", "form": {"logDate": "Log Date", "operationType": "Operation Type", "status": "Status", "pmaCertificate": "PMA Certificate", "description": "Description", "selectOperationType": "Select operation type", "selectStatus": "Select status", "selectPmaCertificate": "Select PMA certificate", "descriptionPlaceholder": "Enter maintenance log details...", "descriptionCharacterCount": "{current}/{max} characters", "descriptionMinLength": "Description must be at least {min} characters long", "descriptionNearLimit": "Only {remaining} characters remaining", "submit": "Create Maintenance Log", "submitting": "Creating...", "noPmaCertificates": "No PMA certificates available"}, "errors": {"title": "Error", "failedToCreate": "Failed to create maintenance log. Please try again.", "failedToLoadPma": "Failed to load PMA certificates", "failedToLoadProject": "Failed to load project details", "projectNotFound": "Project not found or an error occurred."}}, "detail": {"title": "Maintenance Log Details", "close": "Close"}}}, "auth": {"loginTitle": "Login to your account", "loginSubtitle": "Enter your email below to login to your account", "registerTitle": "Create your account", "registerSubtitle": "Enter your information below to create your account", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "fullName": "Full Name", "phoneNumber": "Phone Number", "role": "Role", "emailPlaceholder": "<EMAIL>", "fullNamePlaceholder": "<PERSON>", "phonePlaceholder": "+*********** or **********", "forgotPassword": "Forgot your password?", "selectRole": "Select your role", "jkr": "JKR (Jabatan Kerja Raya)", "jkrPic": "JKR PIC (Person in Charge)", "jkrAdmin": "JKR Admin", "admin": "Administrator", "contractor": "Contractor", "viewer": "Viewer", "client": "Client", "login": "<PERSON><PERSON>", "signUp": "Sign up", "signIn": "Sign in", "createAccount": "Create Account", "signingIn": "Signing in...", "creatingAccount": "Creating Account...", "noAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "phoneTooltip1": "Malaysian phone numbers only", "phoneTooltip2": "e.g., +***********, **********", "passwordRequirements": "Password must contain:", "passwordReq1": "At least 8 characters", "passwordReq2": "One uppercase letter (A-Z)", "passwordReq3": "One lowercase letter (a-z)", "passwordReq4": "One number (0-9)", "passwordReq5": "One special character (@$!%*?&)", "loginSuccess": "Successfully signed in! Redirecting to dashboard...", "loginFailed": "<PERSON><PERSON> failed. Please try again.", "registerSuccess": "Account created successfully! Please check your email to verify your account.", "registerFailed": "Failed to create account. Please try again.", "userExists": "An account with this email already exists. Please try logging in instead.", "profileCreationError": "Registration failed during profile creation. Please try again or contact support.", "signupDisabled": "Account registration is currently disabled. Please contact support.", "adminLoginTitle": "Admin Portal Login", "adminLoginSubtitle": "Access the administrative dashboard", "adminRegisterTitle": "Admin Registration", "adminRegisterSubtitle": "Create your administrator account", "adminLogin": "<PERSON><PERSON>", "adminSignUp": "Admin Sign up", "adminSignIn": "Admin Sign in", "createAdminAccount": "Create Ad<PERSON> Account", "creatingAdminAccount": "Creating <PERSON><PERSON> Account...", "noAdminAccount": "Don't have an admin account?", "alreadyHaveAdminAccount": "Already have an admin account?", "adminLoginSuccess": "Admin login successful! Redirecting to dashboard...", "adminRegisterSuccess": "Admin account created successfully! Please check your email to verify your account.", "adminRegisterSuccessCheckEmail": "Admin account created successfully! Please check your email and click the confirmation link to activate your account.", "contractorLoginTitle": "Contractor <PERSON>", "contractorLoginSubtitle": "Access your contractor dashboard", "contractorRegisterTitle": "Contractor Registration", "contractorRegisterSubtitle": "Join our contractor network", "contractorLogin": "Contractor <PERSON><PERSON>", "contractorSignUp": "Contractor Sign up", "contractorSignIn": "Contractor Sign in", "createContractorAccount": "Create Contractor Account", "creatingContractorAccount": "Creating Contractor Account...", "noContractorAccount": "Don't have a contractor account?", "alreadyHaveContractorAccount": "Already have a contractor account?", "contractorLoginSuccess": "Contractor login successful! Redirecting to dashboard...", "contractorRegisterSuccess": "Contractor account created successfully! Please check your email to verify your account.", "contractorRegisterSuccessCheckEmail": "Contractor account created successfully! Please check your email and click the confirmation link to activate your account.", "contractorNote": "Note: Additional company information will be required during onboarding.", "contractorNoteSubtext": "You'll provide company details, certifications, and project capabilities after registration.", "forgotPasswordTitle": "Reset your password", "forgotPasswordSubtitle": "Enter your email below and we'll send you a reset link", "sendResetLink": "Send Reset Link", "sendingResetLink": "Sending reset link...", "resetLinkSent": "Check your email for the reset link", "resetLinkFailed": "Failed to send reset link. Please try again.", "backToLogin": "Back to login", "verifyCodeTitle": "Enter verification code", "verifyCodeSubtitle": "Enter the 6-digit code sent to your email", "verificationCode": "Verification Code", "codePlaceholder": "Enter 6-digit code", "verifyCode": "Verify Code", "verifyingCode": "Verifying code...", "codeVerified": "Code verified successfully! Redirecting...", "codeVerificationFailed": "Invalid or expired code. Please try again.", "didNotReceiveCode": "Didn't receive the code?", "resendCode": "Resend code", "alreadyHaveCode": "Already have a verification code?", "resetPasswordTitle": "Set your new password", "resetPasswordSubtitle": "Choose a strong password to secure your account", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "newPasswordPlaceholder": "Enter your new password", "confirmPasswordPlaceholder": "Confirm your new password", "updatePassword": "Update Password", "updatingPassword": "Updating password...", "passwordUpdated": "Password updated successfully! Please login with your new password.", "passwordUpdateFailed": "Failed to update password. Please try again.", "checkYourEmail": "Check your email", "emailVerificationSent": "We've sent a verification link to your email address. Please check your inbox and click the link to activate your account.", "emailSentTo": "Email sent to:", "emailVerificationInstructions": "Click the verification link in your email to complete your registration. The link will expire in 24 hours.", "resendEmail": "Resend verification email", "resendingEmail": "Resending...", "emailResent": "Verification email sent successfully!", "emailResendFailed": "Failed to resend email. Please try again.", "emailRequired": "Email address is required to resend verification.", "emailNotReceived": "Didn't receive the email? Check your spam folder.", "checkSpamFolder": "Check your spam folder", "contactSupport": "Contact support"}, "contractor": {"onboarding": {"title": "Contractor Onboarding", "step1": {"title": "Personal Information", "fullName": "Full Name", "fullNamePlaceholder": "Enter your full name", "icNumber": "IC Number", "icNumberPlaceholder": "123456-78-9012", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "01X-XXXXXXX", "role": "Role", "rolePlaceholder": "Select your role", "roleHelp": "Select your role in the company", "technician": "Technician", "admin": "Administrator", "cp": "Competent Person (CP)", "nextButton": "Next: Role Information"}, "step2": {"title": "Role Information - {role}", "nextButton": "Next: Company Setup", "cp": {"title": "Competent Person Information", "name": "Name", "namePlaceholder": "Enter competent person name", "category": "Category", "categoryPlaceholder": "Select CP category", "categoryA": "Category A", "categoryB": "Category B", "categoryC": "Category C", "icNo": "IC Number", "icNoPlaceholder": "123456-78-9012", "cpNumber": "CP Number", "cpNumberPlaceholder": "Enter CP registration number", "tel": "Telephone", "telPlaceholder": "01X-XXXXXXX", "email": "Email", "emailPlaceholder": "<EMAIL>", "liftListFiles": "List of Lift Files", "liftFilesPlateholder": "Upload lift list files (PDF, DOC, DOCX, XLS, XLSX)", "noLiftFiles": "No lift list files uploaded yet", "registrationCertFile": "Registration Certificate File", "registrationCertFilePlaceholder": "Upload registration certificate (PDF, DOC, DOCX, JPG, PNG)", "registrationCertFileHelp": "Upload your CP registration certificate file (minimum 10MB)"}, "admin": {"title": "Administrator Information", "name": "Name", "namePlaceholder": "Enter administrator name", "nameHelp": "This will be used for administrative identification"}, "technician": {"title": "Technician Information", "name": "Name", "namePlaceholder": "Enter technician name", "nameHelp": "This will be used for technician identification"}}, "step3": {"title": "Company Setup", "registrationType": "Registration Type", "createTitle": "Create New Company", "createDescription": "Register a new company that hasn't been registered in the system yet", "joinTitle": "Join Existing Company", "joinDescription": "Join a company that is already registered in the system", "specialCode": "Company Special Code", "specialCodePlaceholder": "Enter the special code provided by the company", "specialCodeHelp": "Enter the special code provided by your company administrator", "continueToCreation": "Continue to Company Creation", "joinCompanyButton": "Join Company"}, "step4": {"title": "Company Creation", "companyCode": "Company Code", "companyCodeHelp": "This unique code will identify your company in the system", "companyName": "Company Name", "companyNamePlaceholder": "Enter your company name", "companyNameHelp": "Company name will be converted to uppercase and must be unique.", "companyNamePreview": "Preview: {name}", "companyType": "Company Type", "companyTypeHelp": "Select your company type", "companyHotline": "Company Hotline", "companyHotlinePlaceholder": "Enter company hotline number", "oemName": "OEM Name", "oemNamePlaceholder": "Enter OEM name", "oemNameHelp": "Required for OEM type companies", "appointedOemCompetentFirm": "Appointed OEM/Competent Firm", "appointedOemCompetentFirmPlaceholder": "Enter appointed OEM or competent firm name", "appointedOemCompetentFirmHelp": "Required for non-competent firms - specify the OEM or competent firm appointed for your services", "competentPersons": {"title": "Competent Persons", "subtitle": "Add competent persons for your company", "addButton": "Add Competent Person", "removeButton": "Remove", "name": "Full Name", "namePlaceholder": "Enter full name", "icNo": "IC Number", "icNoPlaceholder": "123456-78-9012", "phoneNo": "Phone Number", "phoneNoPlaceholder": "+***********", "address": "Address", "addressPlaceholder": "Enter full address", "cpType": "CP Type", "cpTypePlaceholder": "Select CP type", "cpRegisterationNo": "Registration Number", "cpRegisterationNoPlaceholder": "Enter registration number", "cpRegisterationCert": "Registration Certificate", "certExpDate": "Certificate Expiry Date", "noOfPma": "Number of PMAs", "uploadCert": "Upload Certificate", "previewCert": "Click to preview certificate", "types": {"CP1": "CP1 - Competent Person Level 1", "CP2": "CP2 - Competent Person Level 2", "CP3": "CP3 - Competent Person Level 3"}}, "createButton": "Create Company", "creatingButton": "Creating Company..."}, "progress": {"step": "Step {current} of {total}", "personalInfo": "Personal Info", "companyInfo": "Company Info", "finalDetails": "Final Details"}}}, "company": {"types": {"sdn_bhd": "Sdn Bhd", "bhd": "Bhd", "partnership": "Partnership", "sole_proprietorship": "Sole Proprietorship", "llp": "LLP", "competent_firm": "Competent Firm", "non_competent_firm": "Non-Competent Firm", "oem": "OEM (Original Equipment Manufacturer)"}, "availability": {"checking": "Checking availability...", "available": "Company name is available", "unavailable": "Company name is already taken", "error": "Error checking availability"}}, "agencies": {"JKR": "Jabatan Kerja Raya Malaysia (JKR)", "KKM": "Kementerian Kesihatan Malaysia (KKM)", "KPM": "Kementerian Pendidikan Malaysia (KPM)", "KPKT": "Kementerian Perumahan dan <PERSON>mpatan (KPKT)", "KKR": "Kementerian <PERSON> (KKdW)", "KPDNHEP": "Kementerian Perdagangan Dalam Nege<PERSON> dan <PERSON> (KPDNHEP)", "MOSTI": "Kementerian Sains, Teknologi <PERSON> (MOSTI)", "KPWKM": "Kementerian Pembangunan <PERSON>, <PERSON><PERSON><PERSON><PERSON>n <PERSON> (KPWKM)", "DBKL": "Dewan Bandaraya Kuala Lumpur (DBKL)", "MBPJ": "<PERSON><PERSON>aling Jaya (MBPJ)", "MBSJ": "<PERSON><PERSON>araya <PERSON>ang <PERSON> (MBSJ)", "MBSA": "<PERSON><PERSON> (MBSA)", "MPK": "<PERSON><PERSON> (MPK)", "MBIP": "<PERSON><PERSON> (MBIP)", "MPJB": "<PERSON><PERSON> (MPJBT)", "MBPP": "<PERSON><PERSON> (MBPP)", "MPSP": "<PERSON><PERSON> (MPSP)", "MBMB": "<PERSON><PERSON> (MBMB)", "MPAG": "<PERSON><PERSON> (MPAG)", "TNB": "Tenaga Nasional Berhad (TNB)", "SYABAS": "Syarikat Bekalan Air Selangor (SYABAS)", "IWK": "Indah Water Konsortium (IWK)", "PLUS": "PLUS Malaysia Berhad", "KTMB": "<PERSON><PERSON><PERSON> (KTMB)", "MRT": "Mass Rapid Transit Corporation (MRT Corp)", "LRT": "Rapid Rail Sdn Bhd", "HSB": "Hospital Selayang", "HKL": "Hospital Kuala Lumpur", "HUSM": "Hospital Universiti Sains Malaysia (HUSM)", "HUKM": "Hospital Universiti Kebangsaan Malaysia (HUKM)", "UM": "Universiti Malaya (UM)", "UKM": "Universiti Kebangsaan Malaysia (UKM)", "USM": "Universiti Sains Malaysia (USM)", "UTM": "Universiti Teknologi Malaysia (UTM)", "UPM": "Universiti Putra Malaysia (UPM)", "UiTM": "Universiti Teknologi MARA (UiTM)", "Other": "Other"}, "states": {"JH": "<PERSON><PERSON>", "KD": "Kedah", "KT": "<PERSON><PERSON><PERSON>", "ML": "<PERSON><PERSON>", "NS": "<PERSON><PERSON><PERSON>", "PH": "<PERSON><PERSON>", "PN": "<PERSON><PERSON><PERSON>", "PK": "<PERSON><PERSON>", "PL": "<PERSON><PERSON>", "SB": "Sabah", "SW": "Sarawak", "SL": "Selangor", "TR": "Terengganu", "WP": "W.P. Kuala Lumpur", "LBN": "<PERSON><PERSON><PERSON><PERSON>", "PW": "<PERSON><PERSON><PERSON><PERSON>", "OTH": "Other"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "emailSubaddressing": "Email addresses with sub-addressing (+ character) are not allowed", "password": "Password must be at least 8 characters", "passwordMin": "Password must be at least 8 characters", "passwordLowercase": "Password must contain at least one lowercase letter", "passwordUppercase": "Password must contain at least one uppercase letter", "passwordNumber": "Password must contain at least one number", "passwordSpecial": "Password must contain at least one special character (@$!%*?&)", "passwordMatch": "Passwords do not match", "nameMin": "Name must be at least 2 characters", "phoneFormat": "Please enter a valid Malaysian phone number", "roleRequired": "Please select a role", "icNumber": "Please enter a valid IC number"}, "errors": {"somethingWrong": "Something went wrong", "tryAgain": "Please try again", "networkError": "Network error. Please check your connection.", "unauthorized": "You are not authorized to perform this action", "notFound": "The requested resource was not found"}, "invitation": {"loading": "Loading invitation...", "title": "You're Invited!", "description": "{invite<PERSON><PERSON><PERSON>} has invited you to join a project", "someone": "Someone", "projectDetails": {"title": "Project Details", "project": "Project", "role": "Role", "invitedBy": "Invited by", "unknownProject": "Unknown Project", "member": "Member", "unknown": "Unknown"}, "nextStep": {"title": "Next step", "description": "You'll be asked to create a password to set up your SimPLE account."}, "button": {"accepting": "Accepting...", "accept": "Accept Invitation", "acceptAndSetup": "Accept & Set Up Account"}, "disclaimer": "By accepting this invitation, you agree to join this project and collaborate with the team members.", "invalid": {"title": "Invalid Invitation", "description": "This invitation link is invalid, has expired, or has already been used. Please contact the person who invited you for a new invitation."}, "error": {"title": "Error", "description": "An error occurred while loading the invitation. Please try again or contact support."}, "accepted": {"title": "Invitation Accepted!", "description": "You've successfully joined {projectName}. Redirecting to the project dashboard...", "unknownProject": "the project"}, "redirecting": "Redirecting to project...", "accept": {"title": "Project Invitation", "description": "You've been invited to join a project", "success": "Successfully joined {projectName}!", "error": "Failed to accept invitation: {error}", "expired": "This invitation has expired or is invalid", "loading": "Processing invitation...", "acceptButton": "Accept Invitation", "loginRequired": "Please log in to accept this invitation"}}, "error": {"title": "Something went wrong", "description": "We encountered an unexpected error. Please try again or go back to the previous page.", "retryButton": "Try again", "goBackButton": "Go back"}, "notFound": {"title": "404", "description": "Page not found", "backButton": "Back to Home"}, "authPortal": {"title": "Welcome to SimPLE", "subtitle": "Choose your role to access the appropriate portal", "adminPortalTitle": "Administrator <PERSON>", "adminPortalDescription": "For property owners, facility managers, and system administrators", "contractorPortalTitle": "Contractor Portal", "contractorPortalDescription": "For registered contractors and service providers", "adminLogin": "<PERSON><PERSON>", "adminRegister": "Admin Register", "contractorLogin": "Contractor <PERSON><PERSON>", "contractorRegister": "Contractor Register", "helpText": "Need help? Contact support for assistance.", "heroTitle": "Professional Platform", "heroSubtitle": "Connecting administrators and contractors seamlessly", "scrollToLearn": "Scroll to learn more", "adminFeatures": {"projectManagement": "Project Management & Oversight", "userManagement": "User Management & Access Control", "reports": "Reports & Analytics Dashboard"}, "contractorFeatures": {"bidding": "Daily Log – Maintenance & Complaints", "tracking": "Progress Tracking & Reporting", "compliance": "Document Management & Compliance"}, "aboutSimple": {"title": "What is SimPLE?", "subtitle": "Your Building’s Lift. Smarter.", "description": "SimPLE is a centralised platform for intelligent lift monitoring and maintenance management, designed to ensure optimal safety, performance, and uptime. Developed to support building owners, facility managers, and maintenance providers, SimPLE delivers:", "features": {"digital": {"title": "Real-time visibility into lift system performance", "description": "Monitor lift operations and performance metrics instantly."}, "compliance": {"title": "Centralised logging of maintenance activities", "description": "Keep a unified record of all maintenance tasks for easy audits."}, "efficiency": {"title": "Streamlined issue reporting and resolution", "description": "Report and resolve lift issues quickly with an efficient workflow."}, "transparency": {"title": "Actionable insights through performance analytics", "description": "Gain clarity with data-driven reports and analytics."}}, "descrt": "Whether for residential, commercial, or institutional properties, SimPLE empowers you to take full control of lift operations—reducing risks, lowering costs, and improving service quality."}, "nav": {"home": "Home", "about": "About", "whatIsSimple": "What is SimPLE?", "contact": "Contact"}, "services": {"title": "Our Services", "subtitle": "Comprehensive lift management solutions for modern buildings", "liftManagement": {"title": "Lift Asset Lifecycle Management", "description": "End-to-end digital management of lift systems, from installation to maintenance and decommissioning.", "features": {"maintenance": "Scheduled Preventive Maintenance", "inspection": "Safety Inspection Tracking", "compliance": "Digital Logbook & Compliance Monitoring"}}, "safety": {"title": "Safety & Compliance", "description": "Ensuring safety standards and regulatory compliance", "features": {"standards": "International Safety Standards", "certification": "Professional Certification Programs", "reporting": "Risk Mitigation Documentation"}}, "platform": {"title": "Intelligent Operations Platform", "description": "Advanced technology for seamless operations", "features": {"realtime": "Real-Time System Monitoring & Notifications", "analytics": "Customizable Analytics Dashboards", "integration": "Seamless Integration with Third-Party Systems"}}}, "blog": {"title": "Latest News & Insights", "subtitle": "Stay updated with industry trends and best practices", "readMore": "Read More", "viewAll": "View All Posts", "post1": {"date": "December 15, 2024", "title": "Best Practices in Lift Maintenance", "excerpt": "Learn about the latest maintenance strategies that ensure optimal performance and safety."}, "post2": {"date": "December 10, 2024", "title": "Safety Regulations Update 2024", "excerpt": "Important updates to lift safety regulations and compliance requirements for this year."}, "post3": {"date": "December 5, 2024", "title": "Digital Transformation in Lift Management", "excerpt": "How modern technology is revolutionizing the way we manage and maintain elevator systems."}}, "newsletter": {"title": "Stay Connected", "subtitle": "Get the latest updates, industry news, and exclusive insights delivered to your inbox", "emailPlaceholder": "Enter your email address", "subscribe": "Subscribe", "privacy": "We respect your privacy. Unsubscribe at any time."}}, "cpList": {"title": "Competent Persons List", "description": "Manage competent persons in your company", "addCP": "Add CP", "addFirstCP": "Add First CP", "totalCPs": "Total CPs", "byType": "By Type", "expiredCerts": "Expired Certs", "totalPMAs": "Total PMAs", "noCompetentPersons": {"title": "No Competent Persons Found", "description": "You haven't added any competent persons to your company yet. Add your first competent person to get started."}, "accessRestricted": {"title": "Access Restricted", "description": "This feature is only available to contractor users."}, "errorLoading": {"title": "Error Loading CP List", "description": "Failed to load competent persons"}, "addCPModal": {"title": "Add Competent Person", "editTitle": "Edit Competent Person", "submitButton": "Add CP", "updateButton": "Update CP", "cancelButton": "Cancel", "success": {"title": "Success", "description": "Competent person added successfully"}, "updateSuccess": {"title": "Success", "description": "Competent person updated successfully"}, "error": {"title": "Error", "description": "Failed to add competent person"}, "updateError": {"title": "Error", "description": "Failed to update competent person"}}, "fields": {"name": "Name", "icNo": "IC Number", "phone": "Phone", "address": "Address", "cpType": "CP Type", "registrationNo": "Registration No", "registrationCert": "Registration Certificate", "certExpiry": "Cert <PERSON>ry", "pmas": "PMAs", "contact": "Contact", "viewCert": "View Cert", "expired": "Expired", "valid": "<PERSON><PERSON>", "notSpecified": "Not specified", "invalidDate": "Invalid date", "editAction": "Edit", "requiredField": "Required", "optionalField": "Optional"}}, "admin": {"onboarding": {"title": "Complete Your Admin Setup", "description": "Configure your administrative access and monitoring preferences to optimize your JKR lift management experience.", "form": {"title": "Administrative Preferences"}, "saving": "Saving your preferences..."}}, "complaints": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Manage fault reports and maintenance requests", "continue": "Continue", "stats": {"totalReports": "Total Reports", "submitted": "Submitted", "completed": "Completed", "inProgress": "In Progress", "verified": "Verified", "pendingApproval": "Pending Approval", "underReview": "Pending Approval", "approved": "Approved", "rejected": "Rejected", "openReports": "Open Reports", "closedReports": "Closed Reports", "outstanding": "Outstanding (Urgent)", "overdueDescription": "Overdue reports"}, "table": {"title": "Complaint Reports", "subtitle": "Manage and track your fault reports with follow-up capabilities", "reportId": "Ticket ID", "dateSubmitted": "Submitted Date", "pmaNumber": "PMA No", "location": "Location", "issueSummary": "Issue Summary", "status": "Status", "completionDate": "Completion Date", "followUp": "Follow-up", "verificationStatus": "Verification Status", "contractor": "Contractor", "cost": "Cost (RM)", "actions": "Action", "newReport": "New Report", "advancedFilter": "Advanced Filter"}, "filters": {"title": "Filter Reports", "project": "Project", "projectPlaceholder": "Select project", "pmaNo": "PMA No", "pmaNoPlaceholder": "Enter PMA number", "admin": "Admin", "adminPlaceholder": "Select admin", "status": "Status", "statusPlaceholder": "Select status", "dateRange": "Date Range", "resetFilters": "Reset Filters", "allStatus": "All Status"}, "charts": {"weeklyTrend": "Weekly Report Submission Trend", "statusDistribution": "Report Status Distribution", "chartImplementation": "Chart will be implemented"}, "exportReport": "Export Report", "createAduan": "Create Report", "form": {"title": "<PERSON><PERSON> Complaint", "subtitle": "Lift Damage Complaint Management System", "sectionA": {"title": "Complaint Information", "email": "Email", "name": "Name", "complaintDate": "Complaint Date", "agency": "Agency", "contractorCompanyName": "Company / Contractor Name", "location": "Location", "pmaNumber": "PMA Lift Number", "damageDescription": "<PERSON><PERSON> Complaint", "expectedCompletionDate": "Expected Completion Date", "involvesManTrap": "Involves Man Trap", "yes": "Yes", "no": "No"}, "sectionB": {"title": "Repair Information", "note": "This section can be filled after repair is completed or now if information is already available", "actualCompletionDate": "Actual Completion Date", "repairCompletionTime": "Completion Time", "causeOfDamage": "Damage Cause", "correctionAction": "Repair Action", "proofOfRepair": "Proof of Repair", "beforePhoto": "Before Photo", "afterPhoto": "After Photo", "repairCost": "Repair Cost"}, "actions": {"upload": "Upload", "cancel": "Cancel", "updateSectionB": "Update form"}, "notes": {"title": "Important Notes", "required": "All fields marked with (*) are mandatory", "proofRequired": "Proof of repair (photos) must be included after work is completed", "autoSubmit": "Booking will be sent automatically"}, "fileUpload": {"acceptedFormats": "Accepted formats: JPG, PNG, PDF (Max 10MB per file)", "maxFiles": "Maximum 5 files allowed"}, "buttons": {"back": "🔙 Back", "submit": "📤 Sub<PERSON> <PERSON><PERSON><PERSON><PERSON>", "update": "📤 Update Complaint", "submitting": "Submitting...", "updating": "Updating..."}, "placeholders": {"email": "<EMAIL>", "selectDate": "Select date", "selectAgency": "Select agency", "selectPMA": "Select PMA Lift Number", "repairCost": "0.00"}}, "status": {"underReview": "Pending Approval", "approved": "Approved", "rejected": "Rejected", "submitted": "Submitted", "completed": "Completed", "complete": "Complete", "open": "Open", "inProgress": "In Progress", "resolved": "Verified", "pendingApproval": "Pending Approval", "verified": "Verified", "closed": "Closed"}, "followUp": {"inProgress": "In Progress", "pendingApproval": "Pending Approval", "verified": "Verified"}, "actions": {"upload": "Upload", "add": "Add", "resubmit": "Resubmit", "view": "View", "verify": "Verify", "verifySuccess": "<PERSON><PERSON><PERSON><PERSON> verified successfully!", "verifyError": "Failed to verify complaint"}, "verification": {"title": "Confirm Verification", "description": "Please select a verification date and confirm that you want to verify this complaint as completed.", "dateLabel": "Verification Date", "selectDate": "Select verification date", "verifyButton": "<PERSON><PERSON><PERSON>", "verifying": "Verifying...", "verifiedBy": "Verified By:", "verifiedDate": "Verified Date:", "verificationDetails": "Verification Details", "errors": {"selectDateRequired": "Please select a verification date", "userInfoNotAvailable": "User information not available"}}, "pagination": {"showing": "Showing {start}-{end} of {total} entries", "previous": "Previous", "next": "Next"}, "upload": {"title": "Upload Follow-up Documents", "subtitle": "Add supporting documents for reports under review", "description": "Progress photos, additional reports, or requested documents", "chooseFiles": "<PERSON><PERSON>", "takePhoto": "Take Photo"}, "recentActivity": {"title": "Recent Activity", "subtitle": "Latest updates on your complaint reports"}, "common": {"search": "Search", "cancel": "Cancel"}, "urgentSection": {"title": "Outstanding Complaints (Urgent) - Overdue Reports ({count})", "description": "Reports that have passed their expected completion date and require immediate attention", "daysOverdue": "{days} day{plural} overdue", "expectedLabel": "Expected:"}}, "profilePage": {"loading": {"title": "Loading your profile...", "description": "Please wait while we fetch your information"}, "error": {"title": "Unable to Load Profile", "description": "We couldn't load your profile information. This might be a temporary issue.", "refreshPage": "Refresh Page", "goToDashboard": "Go to Dashboard"}, "contractor": {"onboarding": {"title": "Complete Your Registration", "description": "You're almost there! Complete your contractor profile to access all features and start collaborating.", "submitting": "Completing your registration...", "submittingDescription": "This may take a few moments"}, "completed": {"title": "Contractor Profile Complete", "description": "Your contractor registration has been successfully completed and verified.", "profileInformation": "Profile Information", "fullName": "Full Name", "emailAddress": "Email Address", "phoneNumber": "Phone Number", "roleAndStatus": "Role & Status", "contractor": "Contractor", "verifiedRole": "Verified Role", "registrationComplete": "Registration Complete", "allRequirementsFulfilled": "All requirements fulfilled", "goToDashboard": "Go to Dashboard", "accountSettings": "Account <PERSON><PERSON>", "joined": "Joined", "loadingContractorDetails": "Loading contractor details...", "unableToLoadDetails": "Unable to load contractor details. This may indicate an incomplete profile setup or a system error."}}, "jkr": {"title": "Administrator Profile", "description": "Welcome to the JKR Lift Management System administrator panel. Monitor and manage lift maintenance operations across all facilities.", "profileInformation": "Profile Information", "fullName": "Full Name", "emailAddress": "Email Address", "phoneNumber": "Phone Number", "roleAndStatus": "Role & Status", "administrator": "Administrator", "verifiedRole": "Verified Role", "fullAccess": "Full Access", "systemAdministrator": "System Administrator", "systemManagement": "System Management", "description2": "Manage system operations and user access", "manageDashboard": "Manage Dashboard", "systemSettings": "System Settings"}, "client": {"title": "Client Profile", "description": "Welcome to your client portal. Monitor and manage your lift maintenance projects and requests.", "profileInformation": "Profile Information", "fullName": "Full Name", "emailAddress": "Email Address", "phoneNumber": "Phone Number", "roleAndStatus": "Role & Status", "client": "Client", "verifiedRole": "Verified Role", "activeAccount": "Active Account", "projectAccess": "Project Access", "projectManagement": "Project Management", "description2": "Monitor your lift maintenance projects", "viewProjects": "View Projects", "accountSettings": "Account <PERSON><PERSON>", "buildingClient": "Building Owner/Client", "assetOwner": "Asset management and maintenance oversight", "activeStatus": "Active Status", "accountVerified": "Account verified and active", "goToDashboard": "Go to Dashboard", "assetManagement": "Asset Management", "assetManagementDescription": "Quick access to building management and maintenance scheduling", "myBuildings": "My Buildings", "liftStatus": "Lift Status", "maintenance": "Maintenance"}, "fallback": {"message": "Your profile information needs attention. Please complete your setup or contact support for assistance."}, "contractorDetails": {"incomplete": {"title": "Contractor Profile Incomplete", "description": "Your contractor profile setup is not complete. Please complete your onboarding process."}, "companyInformation": {"title": "Company Information", "active": "Active", "companyName": "Company Name", "companyType": "Company Type", "companyCode": "Company Code", "companyHotline": "Company Hotline", "oemName": "OEM Name", "registered": "Registered", "notSpecified": "Not specified", "types": {"competentFirm": "Competent Firm", "nonCompetentFirm": "Non-Competent Firm", "oem": "OEM"}}, "userDetails": {"title": "User Details", "fullName": "Full Name", "email": "Email", "phoneNumber": "Phone Number", "userType": "User Type", "joined": "Joined"}, "documents": {"title": "Documents", "description": "Document management functionality will be available soon.", "uploadRegistration": "Upload Registration Certificate", "uploadLif": "Upload LIF List Files", "registrationModal": {"title": "Upload Registration Certificate", "description": "Upload a new registration certificate. Accepted formats: PDF, JPG, PNG (max 10MB)"}, "lifModal": {"title": "Upload LIF List Files", "description": "Upload LIF list files. You can upload up to 5 files. Accepted formats: PDF, JPG, PNG (max 10MB each)"}}}, "companyForm": {"title": "Company Registration", "description": "Register your company in the contractor database", "sections": {"companyInformation": "Company Information", "oemDetails": "OEM Details", "appointedFirmDetails": "Appointed Firm Details"}, "fields": {"companyCode": {"label": "Company Code", "description": "This unique code will identify your company in the system. Format: YYMM-XXXX-XXXX", "placeholder": "Generating..."}, "companyName": {"label": "Company Name", "placeholder": "Enter your company name"}, "companyType": {"label": "Company Type", "placeholder": "Select your company type", "options": {"competentFirm": "Competent Firm", "nonCompetentFirm": "Non-Competent Firm", "oem": "OEM (Original Equipment Manufacturer)"}}, "companyHotline": {"label": "Company Hotline", "placeholder": "Enter company hotline number"}, "oemName": {"label": "OEM Name", "placeholder": "Enter OEM name", "description": "Required for OEM type companies"}, "appointedFirm": {"label": "Appointed OEM/Competent Firm", "placeholder": "Enter appointed OEM or competent firm name", "description": "Required for non-competent firms - specify the OEM or competent firm appointed for your services"}}, "actions": {"copy": "Copy", "refresh": "Refresh", "cancel": "Cancel", "register": "Register Company", "registering": "Registering..."}, "footer": {"requiredNote": "All fields marked with * are required"}}}}