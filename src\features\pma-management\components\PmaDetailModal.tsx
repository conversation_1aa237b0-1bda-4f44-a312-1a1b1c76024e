'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { format, parseISO } from 'date-fns';
import {
  Calendar,
  Download,
  ExternalLink,
  FileText,
  MapPin,
  User,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { PMACertificate } from '../types/pma-certificate';

interface PmaDetailModalProps {
  pma: PMACertificate | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function PmaDetailModal({
  pma,
  open,
  onOpenChange,
}: PmaDetailModalProps) {
  const tPma = useTranslations('pmaManagement');
  const tCommon = useTranslations('common');

  if (!pma) return null;

  // Calculate status based on expiry date
  const getStatus = () => {
    if (!pma.expiry_date) return 'active';

    const now = new Date();
    const expiry = parseISO(pma.expiry_date);
    const daysDiff = Math.ceil(
      (expiry.getTime() - now.getTime()) / (1000 * 3600 * 24),
    );

    if (daysDiff <= 0) return 'expired';
    if (daysDiff <= 30) return 'expiring_soon';
    return 'active';
  };

  const status = getStatus();

  const getStatusBadge = () => {
    let badgeClassName = '';
    switch (status) {
      case 'active':
        badgeClassName =
          'bg-emerald-50 text-emerald-700 ring-1 ring-inset ring-emerald-600/20';
        break;
      case 'expiring_soon':
        badgeClassName =
          'bg-amber-50 text-amber-700 ring-1 ring-inset ring-amber-600/20';
        break;
      case 'expired':
        badgeClassName =
          'bg-red-50 text-red-700 ring-1 ring-inset ring-red-600/20';
        break;
    }
    return (
      <Badge className={cn('font-medium capitalize', badgeClassName)}>
        {tPma(`status.${status}`)}
      </Badge>
    );
  };

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return tCommon('notSet');
    try {
      return format(parseISO(dateString), 'dd MMM yyyy, HH:mm');
    } catch {
      return dateString;
    }
  };

  const handleDownloadCertificate = () => {
    if (pma.file_url) {
      window.open(pma.file_url, '_blank');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="p-2 bg-blue-50 rounded-lg">
              <FileText className="h-5 w-5 text-blue-600" />
            </div>
            <div className="flex flex-col">
              <span className="text-lg font-semibold text-gray-900">
                {tPma('detail.title')}
              </span>
              <span className="text-sm text-gray-500">
                {pma.pma_number || tCommon('notSet')}
              </span>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status and Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">
                  {tPma('table.status')}
                </span>
              </div>
              <div className="ml-6">{getStatusBadge()}</div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">
                  {tPma('table.expiryDate')}
                </span>
              </div>
              <div className="ml-6">
                <span className="text-sm text-gray-900">
                  {pma.expiry_date
                    ? format(parseISO(pma.expiry_date), 'dd MMM yyyy')
                    : tCommon('notSet')}
                </span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Location Information */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                {tPma('form.location')}
              </span>
            </div>
            <div className="ml-6">
              <span className="text-sm text-gray-900">
                {pma.location || tCommon('notSet')}
              </span>
              {pma.state && (
                <span className="text-sm text-gray-500 ml-2">
                  ({pma.state})
                </span>
              )}
            </div>
          </div>

          <Separator />

          {/* Competent Person Information */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                {tPma('form.competentPerson')}
              </span>
            </div>
            <div className="ml-6">
              <span className="text-sm text-gray-900">
                {pma.competent_person_id || tCommon('notSet')}
              </span>
            </div>
          </div>

          <Separator />

          {/* Timestamps */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">
                  {tPma('detail.dateCreated')}
                </span>
              </div>
              <div className="ml-6">
                <span className="text-sm text-gray-900">
                  {formatDate(pma.created_at)}
                </span>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">
                  {tPma('detail.lastUpdated')}
                </span>
              </div>
              <div className="ml-6">
                <span className="text-sm text-gray-900">
                  {formatDate(pma.updated_at)}
                </span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Certificate File */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                {tPma('form.pdfUpload')}
              </span>
            </div>
            <div className="ml-6">
              {pma.file_url ? (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDownloadCertificate}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  {tPma('detail.downloadCertificate')}
                  <ExternalLink className="h-3 w-3" />
                </Button>
              ) : (
                <span className="text-sm text-gray-500">
                  {tPma('detail.noCertificateUploaded')}
                </span>
              )}
            </div>
          </div>
        </div>

        <div className="flex justify-end pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {tCommon('close')}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
