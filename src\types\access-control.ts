/**
 * Access control types for users
 */

export type UserRole = 'admin' | 'contractor' | 'viewer';
export type AdminAccessMode = 'state' | 'project';
export type StateCode =
  | 'JH'
  | 'KD'
  | 'KT'
  | 'ML'
  | 'NS'
  | 'PH'
  | 'PN'
  | 'PK'
  | 'PL'
  | 'SB'
  | 'SW'
  | 'SL'
  | 'TR'
  | 'WP'
  | 'LBN'
  | 'PW'
  | 'OTH';

export interface User {
  id: string;
  name: string;
  email: string;
  user_role: UserRole;
  admin_access_mode?: AdminAccessMode | null; // Required for admin users
  monitoring_state?: StateCode | null; // Required for admin with state access mode
  contractor_id?: string | null;
}

export interface Project {
  id: string;
  name: string;
  code?: string | null;
  state?: StateCode | null;
  contractor_id?: string | null;
  location?: string | null;
  status?: string | null;
  start_date?: string | null;
  end_date?: string | null;
  // ... other project fields
}

/**
 * Access control utility functions
 */
export class AccessControl {
  /**
   * Check if a user can view a specific project
   * @param user - The user to check access for
   * @param project - The project to check access to
   * @param isProjectMember - Whether the user is an explicit member of this project (optional, defaults to false)
   */
  static canUserViewProject(
    user: User,
    project: Project,
    isProjectMember: boolean = false,
  ): boolean {
    if (user.user_role === 'admin') {
      if (user.admin_access_mode === 'project') {
        // Project-based admin can see all projects regardless of state
        return true;
      }

      if (user.admin_access_mode === 'state') {
        // State-based admin can only see projects in their monitoring state
        return user.monitoring_state === project.state;
      }
    }

    if (user.user_role === 'contractor') {
      // Contractors can only see projects they are explicitly members of
      // No blanket access to all company projects for security reasons
      return isProjectMember;
    }

    // Viewer users have different access rules (to be defined)
    return false;
  }

  /**
   * Filter projects based on user's access level
   * @param user - The user to filter projects for
   * @param projects - The projects to filter
   * @param memberProjectIds - Array of project IDs where the user is an explicit member (optional)
   */
  static filterProjectsForUser(
    user: User,
    projects: Project[],
    memberProjectIds: string[] = [],
  ): Project[] {
    if (user.user_role === 'admin') {
      if (user.admin_access_mode === 'project') {
        // Project-based admin can see all projects
        return projects;
      }

      if (user.admin_access_mode === 'state') {
        // State-based admin can only see projects in their monitoring state
        return projects.filter(
          (project) => project.state === user.monitoring_state,
        );
      }
    }

    if (user.user_role === 'contractor') {
      // Contractors can only see projects they are explicitly members of
      // No blanket access to all company projects for security reasons
      return projects.filter((project) =>
        memberProjectIds.includes(project.id),
      );
    }

    // Viewer users return empty array (handled elsewhere)
    return [];
  }

  /**
   * Get SQL WHERE clause for project filtering based on user role
   * @param user - The user to generate SQL filter for
   * @param memberProjectIds - Array of project IDs where the user is an explicit member (optional)
   */
  static getProjectFilterSQL(
    user: User,
    memberProjectIds: string[] = [],
  ): {
    where: string;
    params: Record<string, unknown>;
  } {
    if (user.user_role === 'admin') {
      if (user.admin_access_mode === 'project') {
        // Project-based admin can see all projects - no filter needed
        return { where: '1=1', params: {} };
      }

      if (user.admin_access_mode === 'state') {
        // State-based admin can only see projects in their monitoring state
        return {
          where: 'projects.state = $monitoring_state',
          params: { monitoring_state: user.monitoring_state },
        };
      }
    }

    if (user.user_role === 'contractor') {
      // Contractors can only see projects they are explicitly members of
      // No blanket access to all company projects for security reasons
      if (memberProjectIds.length === 0) {
        return { where: '1=0', params: {} }; // No access
      }
      return {
        where: `projects.id IN (${memberProjectIds.map((_, i) => `$project_id_${i}`).join(', ')})`,
        params: memberProjectIds.reduce(
          (acc, id, i) => {
            acc[`project_id_${i}`] = id;
            return acc;
          },
          {} as Record<string, unknown>,
        ),
      };
    }

    // Default: no access
    return { where: '1=0', params: {} };
  }
}
