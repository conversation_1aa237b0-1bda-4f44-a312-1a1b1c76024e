'use client';

import { Button } from '@/components/ui/button';
import { PmaForm } from '@/features/pma-management/components/pma-form';
import {
  usePMACertificate,
  useUpdatePMACertificate,
} from '@/features/pma-management/hooks/use-pma-certificates';
import { uploadToOBS } from '@/lib/obs-upload';
import { useProjectContext } from '@/providers/project-context';
import { FileText } from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';

export default function EditPmaPage() {
  const router = useRouter();
  const params = useParams();
  const { pmaId, locale } = params;
  const { selectedProjectId } = useProjectContext();
  const [isUploading, setIsUploading] = useState(false);

  const { data: pmaCertificate, isLoading } = usePMACertificate(
    pmaId as string,
  );
  const { mutate: updatePma, isPending: isUpdating } =
    useUpdatePMACertificate();

  const handleSubmit = async (
    data:
      | {
          pmas: {
            location: string;
            pmaNumber: string;
            inspectionDate: string;
            competentPersonId: string;
            pmaExpiryDate: string;
            pdfFile?: File;
          }[];
        }
      | {
          pmas: {
            pmaExpiryDate: string;
            location?: string;
            pmaNumber?: string;
            inspectionDate?: string;
            competentPersonId?: string;
            pdfFile?: File;
          }[];
        },
  ) => {
    const pmaToUpdate = data.pmas[0];
    if (!pmaToUpdate) return;

    // Validate required fields are present
    if (!pmaToUpdate.pmaExpiryDate) {
      console.error('PMA expiry date is required');
      return;
    }

    if (!selectedProjectId) {
      console.error('Project ID is not selected.');
      return;
    }

    setIsUploading(true);

    try {
      let fileUrl: string | undefined;

      // Upload PDF file if provided
      if (pmaToUpdate.pdfFile) {
        try {
          fileUrl = await uploadToOBS({
            file: pmaToUpdate.pdfFile,
            folder: `projects/${selectedProjectId}/pma-certificates`,
          });
        } catch (error) {
          console.error('Failed to upload PDF:', error);
          // Continue without file URL if upload fails
        }
      }

      updatePma(
        {
          id: pmaId as string,
          updates: {
            pma_number:
              pmaToUpdate.pmaNumber || pmaCertificate?.pma_number || '',
            location: pmaToUpdate.location || pmaCertificate?.location || '',
            expiry_date: pmaToUpdate.pmaExpiryDate,
            competent_person_id:
              pmaToUpdate.competentPersonId ||
              pmaCertificate?.competent_person_id ||
              '',
            ...(fileUrl && { file_url: fileUrl }),
          },
        },
        {
          onSuccess: () => {
            router.push(`/${locale}/pmas`);
          },
        },
      );
    } catch (error) {
      console.error('Error updating PMA:', error);
    } finally {
      setIsUploading(false);
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  const initialData = pmaCertificate
    ? {
        pmas: [
          {
            pmaNumber: pmaCertificate.pma_number || '',
            location: pmaCertificate.location || '',
            inspectionDate: pmaCertificate.inspection_date
              ? new Date(pmaCertificate.inspection_date as string)
                  .toISOString()
                  .split('T')[0]
              : '',
            competentPersonId: pmaCertificate.competent_person_id || '',
            pmaExpiryDate: pmaCertificate.expiry_date
              ? pmaCertificate.expiry_date
              : '',
            pdfFile: undefined, // Files can't be pre-filled in forms
          },
        ],
      }
    : undefined;

  return (
    <div className="bg-gradient-to-br from-slate-50 via-white to-slate-50/50">
      {/* Enhanced Header with Breadcrumbs */}
      <div className="sticky top-0 z-40 bg-white/80 backdrop-blur-lg border-b border-slate-200/60">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Breadcrumbs */}
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Link
                href={`/${locale}/pmas`}
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                <div className="flex items-center space-x-2">
                  <FileText className="h-4 w-4" />
                  <span>PMA Management</span>
                </div>
              </Link>
              <span className="text-muted-foreground">/</span>
              <span className="text-primary font-medium">
                Edit PMA #{pmaCertificate?.pma_number}
              </span>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={() => router.push(`/${locale}/pmas`)}
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
              Edit PMA Entry
            </h1>
            <p className="mt-2 text-lg text-muted-foreground">
              Update the details for this PMA entry.
            </p>
          </div>

          {/* Form */}
          <PmaForm
            onSubmit={handleSubmit}
            isLoading={isUpdating || isUploading}
            initialData={initialData}
            isEditMode
          />
        </div>
      </div>
    </div>
  );
}
