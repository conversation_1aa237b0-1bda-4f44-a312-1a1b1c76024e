/**
 * Email validation utilities
 * Includes validation to prevent email sub-addressing (+ trick)
 */

/**
 * Validates email format and prevents sub-addressing
 * @param email - The email to validate
 * @returns boolean indicating if email is valid
 */
export function isValidEmailWithoutSubaddressing(email: string): boolean {
  // Basic email format check
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  if (!emailRegex.test(email)) {
    return false;
  }
  
  // Check for sub-addressing (+ character in local part)
  const [localPart] = email.split('@');
  if (localPart.includes('+')) {
    return false;
  }
  
  return true;
}

/**
 * Custom Zod email validation that prevents sub-addressing
 * @param email - The email to validate
 * @returns boolean indicating if email is valid
 */
export function validateEmailWithoutSubaddressing(email: string): boolean {
  return isValidEmailWithoutSubaddressing(email);
}
